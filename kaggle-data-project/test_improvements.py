"""
Test script to verify the improvements in dataset ID extraction.
"""

import pandas as pd
from extract_all_pdfs import process_pdf

def test_specific_cases():
    """Test specific cases that were failing."""
    
    # Test cases from partial_extraction.csv that were not found
    test_cases = [
        ("10.1002_ece3.6144", "https://doi.org/10.5061/dryad.zw3r22854"),
        ("10.1002_ece3.6303", "https://doi.org/10.5061/dryad.37pvmcvgb"),
        ("10.1002_esp.5058", "https://doi.org/10.5061/dryad.jh9w0vt9t"),
        ("10.1007_s00259-022-06053-8", "https://doi.org/10.7937/k9/tcia.2017.7hs46erv"),
        ("10.1007_s00382-022-06361-7", "https://doi.org/10.6075/j0154fjj"),
        ("10.1007_s00442-022-05201-z", "https://doi.org/10.5061/dryad.wpzgmsbps")
    ]
    
    print("=== Testing Improved Dataset ID Extraction ===\n")
    
    for article_id, dataset_id in test_cases:
        print(f"Testing: {article_id}")
        print(f"Dataset ID: {dataset_id}")
        
        result = process_pdf(article_id, dataset_id)
        
        print(f"Result: {result[2]}")
        print("-" * 60)
        print()

if __name__ == "__main__":
    test_specific_cases()
